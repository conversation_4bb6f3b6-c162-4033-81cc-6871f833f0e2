# C++ objects and libs
*.slo
*.lo
*.o
*.a
*.la
*.lai
*.so
*.so.*
*.dll
*.dylib

# Qt-es
object_script.*.Release
object_script.*.Debug
*_plugin_import.cpp
/.qmake.cache
/.qmake.stash
*.pro.user
*.pro.user.*
*.qbs.user
*.qbs.user.*
*.moc
moc_*.cpp
moc_*.h
qrc_*.cpp
ui_*.h
*.qmlc
*.jsc
Makefile*
*build-*
*.qm
*.prl

# Qt unit tests
target_wrapper.*

# QtCreator
*.autosave

# QtCreator Qml
*.qmlproject.user
*.qmlproject.user.*

# QtCreator CMake
CMakeLists.txt.user*

# QtCreator 4.8< compilation database
compile_commands.json

# QtCreator local machine specific files for imported projects
*creator.user*

*_qmlcache.qrc

# NAFSim specific directories
build/                  # 编译中间文件
logs/                   # 日志文件
temp/                   # 临时文件
bin/debug/             # Debug版本可执行文件
bin/release/           # Release版本可执行文件

# IDE specific files
.idea/                 # IntelliJ IDEA
.vscode/               # Visual Studio Code
*.user                 # Qt Creator user files

# OS specific files
.DS_Store              # macOS
Thumbs.db              # Windows
*.tmp                  # 临时文件
*.log                  # 日志文件
