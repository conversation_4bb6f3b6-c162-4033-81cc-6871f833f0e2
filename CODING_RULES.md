# NAFSim 项目开发规范

## 项目概述
- **项目名称**: NAFSim (军事仿真类游戏)
- **开发框架**: Qt 5.14.2
- **C++标准**: C++17
- **构建系统**: qmake
- **开发平台**: macOS
- **Qt路径**: /Users/<USER>/Qt5.14.2/5.14.2/clang_64/bin/qmake

## 代码风格规范

### 1. 命名规范
- **类名**: 使用帕斯卡命名法 (PascalCase)
  ```cpp
  class SimulationEngine;
  class WeaponSystem;
  ```
- **函数名**: 使用驼峰命名法 (camelCase)
  ```cpp
  void startSimulation();
  bool isTargetInRange();
  ```
- **变量名**: 使用驼峰命名法
  ```cpp
  int targetCount;
  double fireRange;
  ```
- **常量**: 使用全大写加下划线
  ```cpp
  const int MAX_UNITS = 1000;
  const double PI_VALUE = 3.14159;
  ```
- **成员变量**: 使用m_前缀
  ```cpp
  int m_unitId;
  QString m_unitName;
  ```

### 2. 文件组织
- **头文件**: .h 扩展名
- **源文件**: .cpp 扩展名
- **UI文件**: .ui 扩展名
- **资源文件**: .qrc 扩展名
- **项目文件**: .pro 扩展名

### 3. 包含顺序
```cpp
// 1. 对应的头文件
#include "simulationengine.h"

// 2. Qt头文件
#include <QApplication>
#include <QWidget>

// 3. 标准库头文件
#include <iostream>
#include <vector>

// 4. 第三方库头文件
#include <boost/algorithm.hpp>
```

## 注释规范 (Doxygen风格)

### 1. 类注释
```cpp
/**
 * @brief 仿真引擎类
 * 
 * 负责管理整个军事仿真系统的核心逻辑，包括单位管理、
 * 战斗计算、地图处理等功能。
 * 
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */
class SimulationEngine : public QObject
{
    Q_OBJECT
    
public:
    /**
     * @brief 构造函数
     * @param parent 父对象指针
     */
    explicit SimulationEngine(QObject *parent = nullptr);
};
```

### 2. 函数注释
```cpp
/**
 * @brief 计算两点之间的距离
 * 
 * 使用欧几里得距离公式计算二维平面上两点间的直线距离。
 * 主要用于判断单位之间的相对位置关系。
 * 
 * @param point1 第一个点的坐标
 * @param point2 第二个点的坐标
 * @return double 返回两点间的距离值
 * 
 * @note 距离单位为像素
 * @warning 输入坐标不能为负值
 */
double calculateDistance(const QPointF& point1, const QPointF& point2);
```

### 3. 变量注释
```cpp
private:
    int m_maxUnits;          ///< 最大单位数量限制
    QTimer* m_updateTimer;   ///< 更新定时器指针
    bool m_isRunning;        ///< 仿真运行状态标志
```

## 编程模式要求

### 1. 使用成熟设计模式
- **单例模式**: 用于全局管理器类
- **观察者模式**: 用于事件通知
- **工厂模式**: 用于对象创建
- **策略模式**: 用于算法选择

### 2. 避免复杂编程技巧
- 不使用过度的模板元编程
- 避免复杂的宏定义
- 不使用晦涩的C++特性
- 优先使用标准库而非自定义实现

### 3. 错误处理
```cpp
/**
 * @brief 加载配置文件
 * @param filePath 配置文件路径
 * @return bool 加载成功返回true，失败返回false
 */
bool loadConfig(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "无法打开配置文件:" << filePath;
        return false;
    }
    
    // 处理文件内容...
    return true;
}
```

## Qt特定规范

### 1. 信号槽连接
```cpp
// 优先使用新语法
connect(button, &QPushButton::clicked, 
        this, &MainWindow::onButtonClicked);

// 避免使用旧语法
// connect(button, SIGNAL(clicked()), this, SLOT(onButtonClicked()));
```

### 2. 内存管理
```cpp
// 使用Qt父子关系管理内存
QWidget* widget = new QWidget(this);  // this作为父对象

// 或使用智能指针
std::unique_ptr<QTimer> timer = std::make_unique<QTimer>();
```

### 3. 字符串处理
```cpp
// 优先使用QString
QString message = QStringLiteral("仿真开始");

// 格式化字符串
QString info = QString("单位数量: %1, 时间: %2")
               .arg(unitCount)
               .arg(currentTime);
```

## 项目目录结构

### 完整目录结构
```
NAFSim/
├── bin/                    # 可执行文件和编译输出的动态库
│   ├── debug/             # Debug版本输出
│   └── release/           # Release版本输出
├── src/                   # 源代码目录
│   ├── core/              # 核心逻辑
│   ├── ui/                # 用户界面
│   ├── simulation/        # 仿真模块
│   └── utils/             # 工具类
├── include/               # 公共头文件
│   ├── public/            # 对外接口头文件
│   └── third_party/       # 第三方库头文件
├── lib/                   # 第三方库
│   ├── static/            # 静态库
│   └── dynamic/           # 动态库
├── resources/             # Qt资源文件
│   ├── images/            # 图片资源
│   ├── icons/             # 图标
│   └── qrc/               # Qt资源文件
├── config/                # 配置文件
│   ├── settings/          # 应用设置
│   └── scenarios/         # 仿真场景
├── data/                  # 数据文件
│   ├── maps/              # 地图数据
│   ├── units/             # 单位数据
│   └── weapons/           # 武器数据
├── docs/                  # 文档
│   ├── api/               # API文档
│   ├── user_manual/       # 用户手册
│   └── design/            # 设计文档
├── tests/                 # 测试代码
│   ├── unit_tests/        # 单元测试
│   └── integration_tests/ # 集成测试
├── example/               # 示例代码
├── scripts/               # 构建和部署脚本
├── tools/                 # 工具程序
├── build/                 # 编译中间文件（.gitignore）
├── logs/                  # 日志文件（.gitignore）
├── temp/                  # 临时文件（.gitignore）
├── .gitignore            # Git忽略文件
├── README.md             # 项目说明
├── CODING_RULES.md       # 编码规范
└── NAFSim.pro           # qmake项目文件
```

### 目录职责说明

| 目录 | 职责 | 说明 |
|------|------|------|
| `bin/` | 存放编译输出的可执行文件和项目生成的动态库 | 按Debug/Release分类存放 |
| `src/` | 项目源代码 | 按功能模块组织 |
| `include/` | 公共头文件 | 便于模块化开发和对外接口 |
| `lib/` | 第三方库文件 | 静态库和动态库分别存放 |
| `resources/` | Qt资源文件 | 图片、图标、qrc文件等 |
| `config/` | 配置文件 | 应用设置和仿真场景配置 |
| `data/` | 业务数据文件 | 地图、单位、武器等游戏数据 |
| `docs/` | 项目文档 | API文档、用户手册、设计文档 |
| `tests/` | 测试代码 | 单元测试和集成测试 |
| `example/` | 示例代码 | 帮助用户理解项目使用方法 |
| `scripts/` | 自动化脚本 | 构建、部署、清理等脚本 |
| `tools/` | 辅助工具程序 | 地图编辑器等开发工具 |
| `build/` | 编译中间文件 | 编译过程产生的临时文件 |
| `logs/` | 日志文件 | 程序运行时产生的日志 |
| `temp/` | 临时文件 | 运行时临时文件 |

## 编译配置

### qmake配置示例
```pro
QT += core widgets gui

CONFIG += c++17

TARGET = NAFSim
TEMPLATE = app

SOURCES += \
    src/main.cpp \
    src/mainwindow.cpp

HEADERS += \
    src/mainwindow.h

FORMS += \
    src/mainwindow.ui

RESOURCES += \
    resources/resources.qrc
```

## 版本控制规范

### Git提交信息格式
```
类型(范围): 简短描述

详细描述（可选）

相关问题编号（可选）
```

示例:
```
feat(simulation): 添加武器系统模块

实现了基础的武器发射和命中检测功能
包含弹道计算和伤害评估

Closes #123
```

## 测试要求

### 单元测试
```cpp
/**
 * @brief 距离计算测试
 */
void TestSimulation::testCalculateDistance()
{
    QPointF point1(0, 0);
    QPointF point2(3, 4);
    
    double distance = calculateDistance(point1, point2);
    
    QCOMPARE(distance, 5.0);
}
```

## 性能要求

1. **响应时间**: UI操作响应时间不超过100ms
2. **内存使用**: 避免内存泄漏，及时释放资源
3. **CPU使用**: 仿真循环优化，避免不必要的计算

## 代码审查清单

- [ ] 代码符合命名规范
- [ ] 所有类和函数都有中文注释
- [ ] 使用了合适的设计模式
- [ ] 没有内存泄漏
- [ ] 错误处理完善
- [ ] 代码可读性良好
- [ ] 符合Qt编程规范

---

**最后更新**: 2024年
**维护者**: NAFSim开发团队