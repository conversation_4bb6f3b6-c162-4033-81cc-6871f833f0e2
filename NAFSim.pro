#-------------------------------------------------
# NAFSim 项目文件
# 军事仿真类游戏
#-------------------------------------------------

QT += core widgets gui opengl

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++17

# 项目基本信息
TARGET = NAFSim
TEMPLATE = app
VERSION = 1.0.0

# 编译器配置
QMAKE_CXXFLAGS += -std=c++17

# 包含路径
INCLUDEPATH += \
    src \
    src/core \
    src/ui \
    src/simulation \
    src/utils \
    include/public \
    include/third_party

# 源文件
SOURCES += \
    src/main.cpp

# 头文件
HEADERS += 

# UI文件
FORMS += 

# 资源文件
RESOURCES += \
    resources/qrc/images.qrc \
    resources/qrc/icons.qrc

# 输出目录配置
CONFIG(debug, debug|release) {
    DESTDIR = $$PWD/bin/debug
    OBJECTS_DIR = $$PWD/build/debug/obj
    MOC_DIR = $$PWD/build/debug/moc
    RCC_DIR = $$PWD/build/debug/rcc
    UI_DIR = $$PWD/build/debug/ui
}

CONFIG(release, debug|release) {
    DESTDIR = $$PWD/bin/release
    OBJECTS_DIR = $$PWD/build/release/obj
    MOC_DIR = $$PWD/build/release/moc
    RCC_DIR = $$PWD/build/release/rcc
    UI_DIR = $$PWD/build/release/ui
}

# 库路径
LIBS += -L$$PWD/lib/static \
        -L$$PWD/lib/dynamic

# macOS特定配置
macx {
    QMAKE_MACOSX_DEPLOYMENT_TARGET = 10.14
    QMAKE_INFO_PLIST = Info.plist
}

# Windows特定配置
win32 {
    RC_ICONS = resources/icons/nafsim.ico
}

# Linux特定配置
unix:!macx {
    # Linux特定设置
}

# 安装配置
target.path = $$PWD/bin
INSTALLS += target

# 清理配置
QMAKE_CLEAN += $$DESTDIR/$$TARGET

# 编译信息
message("NAFSim项目配置:")
message("- Qt版本: $$QT_VERSION")
message("- 目标平台: $$QMAKESPEC")
message("- 输出目录: $$DESTDIR")
message("- C++标准: C++17")
