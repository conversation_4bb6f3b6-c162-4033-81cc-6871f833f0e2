# NAFSim - Naval Air Force Simulation

一个基于Qt 5.14.2开发的军事仿真类游戏，支持海军和空军单位的战术仿真。

## 项目概述

NAFSim是一个功能完整的军事仿真游戏，提供直观的用户界面和丰富的仿真场景。项目采用现代C++17标准开发，使用Qt框架构建跨平台的图形用户界面。

### 主要特性

- 🚢 **海军单位仿真**: 支持驱逐舰、护卫舰、潜艇等多种海军单位
- ✈️ **空军单位仿真**: 支持战斗机、轰炸机等空军单位（规划中）
- 🎯 **战术仿真**: 真实的武器系统和战斗计算
- 🗺️ **地图系统**: 支持自定义地图和多种地形类型
- 🤖 **AI系统**: 智能的AI对手和多种难度级别
- 🎮 **用户界面**: 直观的操作界面和实时状态显示

## 技术栈

- **开发框架**: Qt 5.14.2
- **编程语言**: C++17
- **构建系统**: qmake
- **目标平台**: macOS (主要), Windows/Linux (后续支持)
- **图形渲染**: Qt Graphics Framework + OpenGL (可选)

## 项目结构

```
NAFSim/
├── bin/                    # 可执行文件和编译输出
├── src/                    # 源代码
│   ├── core/              # 核心逻辑
│   ├── ui/                # 用户界面
│   ├── simulation/        # 仿真模块
│   └── utils/             # 工具类
├── include/               # 公共头文件
├── lib/                   # 第三方库
├── resources/             # 资源文件
├── config/                # 配置文件
├── data/                  # 数据文件
├── docs/                  # 文档
├── tests/                 # 测试代码
├── example/               # 示例代码
├── scripts/               # 构建脚本
└── tools/                 # 工具程序
```

## 快速开始

### 环境要求

- macOS 10.14 或更高版本
- Qt 5.14.2 或更高版本
- Xcode Command Line Tools
- C++17 兼容的编译器

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/xixilidao/NAFSim.git
   cd NAFSim
   ```

2. **配置Qt环境**
   确保Qt 5.14.2已正确安装在 `/Users/<USER>/Qt5.14.2/5.14.2/clang_64/` 路径下

3. **构建项目**
   ```bash
   # 构建Debug版本
   ./scripts/build.sh debug

   # 构建Release版本
   ./scripts/build.sh release
   ```

4. **运行程序**
   ```bash
   # Debug版本
   ./bin/debug/NAFSim.app/Contents/MacOS/NAFSim

   # Release版本
   ./bin/release/NAFSim.app/Contents/MacOS/NAFSim
   ```

### 开发工具

项目提供了便捷的开发脚本：

```bash
# 清理构建文件
./scripts/clean.sh

# 运行测试
./scripts/test.sh

# 构建项目
./scripts/build.sh [debug|release]
```

## 开发指南

### 代码规范

项目遵循严格的代码规范，详见 [CODING_RULES.md](CODING_RULES.md)：

- 使用C++17标准
- 遵循Qt编程规范
- 采用Doxygen风格的中文注释
- 使用成熟的设计模式

### 项目设计

详细的项目设计文档请参考 [docs/design/PROJECT_DESIGN.md](docs/design/PROJECT_DESIGN.md)

### 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 配置文件

### 应用配置

主要配置文件位于 `config/settings/app_config.json`，包含：
- 应用程序设置
- 图形配置
- 音频设置
- 输入配置

### 仿真场景

场景配置文件位于 `config/scenarios/`，定义：
- 地图设置
- 单位配置
- 任务目标
- 胜利条件

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 联系方式

- 项目主页: https://github.com/xixilidao/NAFSim
- 问题反馈: https://github.com/xixilidao/NAFSim/issues
- 邮箱: <EMAIL>

## 致谢

感谢所有为NAFSim项目做出贡献的开发者和测试人员。

---

**注意**: 当前项目处于开发阶段，主要功能正在实现中。欢迎贡献代码和提出建议！
NAFSim
