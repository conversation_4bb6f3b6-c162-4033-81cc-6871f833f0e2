# NAFSim API 参考文档

本文档提供NAFSim项目的完整API参考，包括所有公共类、函数和接口的详细说明。

## 目录

- [核心模块 (Core)](#核心模块-core)
- [仿真模块 (Simulation)](#仿真模块-simulation)
- [用户界面模块 (UI)](#用户界面模块-ui)
- [工具模块 (Utils)](#工具模块-utils)

## 核心模块 (Core)

### SimulationEngine

仿真引擎的核心类，负责管理整个仿真系统。

```cpp
class SimulationEngine : public QObject
{
    Q_OBJECT

public:
    enum State {
        Stopped,    ///< 停止状态
        Running,    ///< 运行状态
        Paused      ///< 暂停状态
    };

    /**
     * @brief 构造函数
     * @param parent 父对象指针
     */
    explicit SimulationEngine(QObject *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~SimulationEngine();

    /**
     * @brief 初始化仿真引擎
     * @return 初始化成功返回true，失败返回false
     */
    bool initialize();

    /**
     * @brief 启动仿真
     * @return 启动成功返回true，失败返回false
     */
    bool start();

    /**
     * @brief 暂停仿真
     */
    void pause();

    /**
     * @brief 停止仿真
     */
    void stop();

    /**
     * @brief 获取当前状态
     * @return 当前仿真状态
     */
    State getState() const;

    /**
     * @brief 设置时间缩放比例
     * @param scale 时间缩放比例 (1.0 = 实时)
     */
    void setTimeScale(double scale);

    /**
     * @brief 获取时间缩放比例
     * @return 当前时间缩放比例
     */
    double getTimeScale() const;

signals:
    /**
     * @brief 状态改变信号
     * @param newState 新状态
     */
    void stateChanged(State newState);

    /**
     * @brief 仿真更新信号
     * @param deltaTime 时间增量
     */
    void simulationUpdated(double deltaTime);

private slots:
    void updateSimulation();

private:
    class Private;
    Private* d;
};
```

### GameManager

游戏状态管理器，负责管理游戏的整体状态和流程。

```cpp
class GameManager : public QObject
{
    Q_OBJECT

public:
    enum GameState {
        MainMenu,       ///< 主菜单
        Loading,        ///< 加载中
        InGame,         ///< 游戏中
        Paused,         ///< 暂停
        GameOver        ///< 游戏结束
    };

    /**
     * @brief 获取游戏管理器实例（单例模式）
     * @return 游戏管理器实例
     */
    static GameManager* instance();

    /**
     * @brief 加载场景
     * @param scenarioFile 场景文件路径
     * @return 加载成功返回true，失败返回false
     */
    bool loadScenario(const QString& scenarioFile);

    /**
     * @brief 开始游戏
     */
    void startGame();

    /**
     * @brief 暂停游戏
     */
    void pauseGame();

    /**
     * @brief 结束游戏
     */
    void endGame();

    /**
     * @brief 获取当前游戏状态
     * @return 当前游戏状态
     */
    GameState getCurrentState() const;

signals:
    void gameStateChanged(GameState newState);
    void scenarioLoaded(const QString& scenarioName);
    void gameStarted();
    void gameEnded();

private:
    GameManager(QObject* parent = nullptr);
    static GameManager* s_instance;
};
```

## 仿真模块 (Simulation)

### Unit

所有仿真单位的基类。

```cpp
class Unit : public QObject
{
    Q_OBJECT

public:
    enum UnitType {
        Naval,      ///< 海军单位
        Air,        ///< 空军单位
        Land        ///< 陆军单位
    };

    /**
     * @brief 构造函数
     * @param id 单位ID
     * @param type 单位类型
     * @param parent 父对象
     */
    Unit(const QString& id, UnitType type, QObject* parent = nullptr);

    /**
     * @brief 虚析构函数
     */
    virtual ~Unit();

    /**
     * @brief 获取单位ID
     * @return 单位ID
     */
    QString getId() const;

    /**
     * @brief 获取单位类型
     * @return 单位类型
     */
    UnitType getType() const;

    /**
     * @brief 获取单位位置
     * @return 单位位置坐标
     */
    QPointF getPosition() const;

    /**
     * @brief 设置单位位置
     * @param position 新位置坐标
     */
    void setPosition(const QPointF& position);

    /**
     * @brief 获取单位朝向
     * @return 朝向角度（度）
     */
    double getHeading() const;

    /**
     * @brief 设置单位朝向
     * @param heading 朝向角度（度）
     */
    void setHeading(double heading);

    /**
     * @brief 获取当前生命值
     * @return 当前生命值
     */
    double getHealth() const;

    /**
     * @brief 获取最大生命值
     * @return 最大生命值
     */
    double getMaxHealth() const;

    /**
     * @brief 受到伤害
     * @param damage 伤害值
     */
    void takeDamage(double damage);

    /**
     * @brief 检查单位是否存活
     * @return 存活返回true，否则返回false
     */
    bool isAlive() const;

    /**
     * @brief 更新单位状态（纯虚函数）
     * @param deltaTime 时间增量
     */
    virtual void update(double deltaTime) = 0;

    /**
     * @brief 渲染单位（纯虚函数）
     * @param painter 绘制器
     */
    virtual void render(QPainter* painter) = 0;

signals:
    void positionChanged(const QPointF& newPosition);
    void headingChanged(double newHeading);
    void healthChanged(double newHealth);
    void unitDestroyed(const QString& unitId);

protected:
    QString m_id;           ///< 单位ID
    UnitType m_type;        ///< 单位类型
    QPointF m_position;     ///< 位置坐标
    double m_heading;       ///< 朝向角度
    double m_health;        ///< 当前生命值
    double m_maxHealth;     ///< 最大生命值
    double m_speed;         ///< 移动速度
};
```

### NavalUnit

海军单位类，继承自Unit。

```cpp
class NavalUnit : public Unit
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param id 单位ID
     * @param unitData 单位数据
     * @param parent 父对象
     */
    NavalUnit(const QString& id, const QJsonObject& unitData, QObject* parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~NavalUnit() override;

    /**
     * @brief 更新单位状态
     * @param deltaTime 时间增量
     */
    void update(double deltaTime) override;

    /**
     * @brief 渲染单位
     * @param painter 绘制器
     */
    void render(QPainter* painter) override;

    /**
     * @brief 发射武器
     * @param weaponType 武器类型
     * @param target 目标位置
     * @return 发射成功返回true，失败返回false
     */
    bool fireWeapon(const QString& weaponType, const QPointF& target);

    /**
     * @brief 获取雷达探测范围
     * @return 雷达探测范围（公里）
     */
    double getRadarRange() const;

    /**
     * @brief 获取声纳探测范围
     * @return 声纳探测范围（公里）
     */
    double getSonarRange() const;

private:
    class Private;
    Private* d;
};
```

## 工具模块 (Utils)

### MathUtils

数学工具类，提供常用的数学计算函数。

```cpp
class MathUtils
{
public:
    /**
     * @brief 计算两点间距离
     * @param point1 第一个点
     * @param point2 第二个点
     * @return 两点间距离
     */
    static double calculateDistance(const QPointF& point1, const QPointF& point2);

    /**
     * @brief 计算两点间角度
     * @param from 起始点
     * @param to 目标点
     * @return 角度（度）
     */
    static double calculateAngle(const QPointF& from, const QPointF& to);

    /**
     * @brief 角度标准化到0-360度范围
     * @param angle 输入角度
     * @return 标准化后的角度
     */
    static double normalizeAngle(double angle);

    /**
     * @brief 线性插值
     * @param start 起始值
     * @param end 结束值
     * @param t 插值参数 (0.0 - 1.0)
     * @return 插值结果
     */
    static double lerp(double start, double end, double t);
};
```

### Logger

日志系统，提供统一的日志记录功能。

```cpp
class Logger
{
public:
    enum LogLevel {
        Debug,      ///< 调试信息
        Info,       ///< 一般信息
        Warning,    ///< 警告信息
        Error,      ///< 错误信息
        Critical    ///< 严重错误
    };

    /**
     * @brief 初始化日志系统
     * @param logFile 日志文件路径
     * @param level 日志级别
     * @return 初始化成功返回true，失败返回false
     */
    static bool initialize(const QString& logFile, LogLevel level = Info);

    /**
     * @brief 记录日志
     * @param level 日志级别
     * @param message 日志消息
     * @param file 源文件名
     * @param line 行号
     */
    static void log(LogLevel level, const QString& message, 
                   const char* file = nullptr, int line = 0);

    /**
     * @brief 设置日志级别
     * @param level 新的日志级别
     */
    static void setLogLevel(LogLevel level);

    /**
     * @brief 刷新日志缓冲区
     */
    static void flush();
};

// 便捷宏定义
#define LOG_DEBUG(msg)    Logger::log(Logger::Debug, msg, __FILE__, __LINE__)
#define LOG_INFO(msg)     Logger::log(Logger::Info, msg, __FILE__, __LINE__)
#define LOG_WARNING(msg)  Logger::log(Logger::Warning, msg, __FILE__, __LINE__)
#define LOG_ERROR(msg)    Logger::log(Logger::Error, msg, __FILE__, __LINE__)
#define LOG_CRITICAL(msg) Logger::log(Logger::Critical, msg, __FILE__, __LINE__)
```

## 使用示例

### 基本使用流程

```cpp
#include "core/SimulationEngine.h"
#include "core/GameManager.h"
#include "simulation/NavalUnit.h"

int main()
{
    // 初始化游戏管理器
    GameManager* gameManager = GameManager::instance();
    
    // 创建仿真引擎
    SimulationEngine engine;
    engine.initialize();
    
    // 加载场景
    gameManager->loadScenario("config/scenarios/scenario_01.json");
    
    // 开始游戏
    gameManager->startGame();
    engine.start();
    
    return 0;
}
```

---

**注意**: 本API文档会随着项目开发进度持续更新。当前大部分类仍在开发中。
