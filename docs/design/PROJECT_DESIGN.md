# NAFSim 项目设计文档

## 项目概述

### 项目信息
- **项目名称**: NAFSim (Naval Air Force Simulation)
- **项目类型**: 军事仿真类游戏
- **开发框架**: Qt 5.14.2
- **编程语言**: C++17
- **构建系统**: qmake
- **目标平台**: macOS (主要), Windows/Linux (后续支持)

### 项目目标
开发一个功能完整的军事仿真游戏，支持海军和空军单位的战术仿真，提供直观的用户界面和丰富的仿真场景。

## 系统架构设计

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面层     │    │   业务逻辑层     │    │   数据访问层     │
│   (UI Layer)    │◄──►│ (Business Layer)│◄──►│  (Data Layer)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
    ┌─────────┐            ┌─────────┐            ┌─────────┐
    │Qt Widgets│            │Core Logic│            │File I/O │
    │Qt Graphics│            │Simulation│            │Database │
    │Qt OpenGL │            │AI Engine │            │Config   │
    └─────────┘            └─────────┘            └─────────┘
```

### 核心模块设计

#### 1. 核心模块 (src/core/)
- **SimulationEngine**: 仿真引擎核心类
- **GameManager**: 游戏状态管理
- **EventSystem**: 事件系统
- **ConfigManager**: 配置管理器

#### 2. 仿真模块 (src/simulation/)
- **Unit**: 基础单位类
- **NavalUnit**: 海军单位
- **AirUnit**: 空军单位
- **WeaponSystem**: 武器系统
- **BattleCalculator**: 战斗计算器
- **AIController**: AI控制器

#### 3. 用户界面模块 (src/ui/)
- **MainWindow**: 主窗口
- **MapView**: 地图视图
- **ControlPanel**: 控制面板
- **StatusBar**: 状态栏
- **DialogManager**: 对话框管理

#### 4. 工具模块 (src/utils/)
- **MathUtils**: 数学工具
- **FileUtils**: 文件工具
- **Logger**: 日志系统
- **Timer**: 定时器工具

## 数据结构设计

### 单位数据结构
```cpp
class Unit {
private:
    int m_id;                    ///< 单位ID
    QString m_name;              ///< 单位名称
    QPointF m_position;          ///< 位置坐标
    double m_health;             ///< 生命值
    double m_maxHealth;          ///< 最大生命值
    double m_speed;              ///< 移动速度
    UnitType m_type;             ///< 单位类型
    
public:
    virtual void update(double deltaTime) = 0;
    virtual void render(QPainter* painter) = 0;
};
```

### 地图数据结构
```cpp
class GameMap {
private:
    int m_width;                 ///< 地图宽度
    int m_height;                ///< 地图高度
    QVector<QVector<TerrainType>> m_terrain;  ///< 地形数据
    QList<Unit*> m_units;        ///< 单位列表
    
public:
    bool isValidPosition(const QPointF& pos);
    TerrainType getTerrainAt(const QPointF& pos);
};
```

## 文件组织规范

### 源代码组织
```
src/
├── core/                      # 核心系统
│   ├── SimulationEngine.h/cpp # 仿真引擎
│   ├── GameManager.h/cpp      # 游戏管理器
│   ├── EventSystem.h/cpp      # 事件系统
│   └── ConfigManager.h/cpp    # 配置管理器
├── simulation/                # 仿真逻辑
│   ├── Unit.h/cpp            # 基础单位类
│   ├── NavalUnit.h/cpp       # 海军单位
│   ├── AirUnit.h/cpp         # 空军单位
│   ├── WeaponSystem.h/cpp    # 武器系统
│   └── AIController.h/cpp    # AI控制器
├── ui/                       # 用户界面
│   ├── MainWindow.h/cpp/ui   # 主窗口
│   ├── MapView.h/cpp         # 地图视图
│   ├── ControlPanel.h/cpp/ui # 控制面板
│   └── StatusBar.h/cpp       # 状态栏
└── utils/                    # 工具类
    ├── MathUtils.h/cpp       # 数学工具
    ├── FileUtils.h/cpp       # 文件工具
    ├── Logger.h/cpp          # 日志系统
    └── Timer.h/cpp           # 定时器
```

### 资源文件组织
```
resources/
├── images/                   # 图片资源
│   ├── units/               # 单位图标
│   ├── terrain/             # 地形贴图
│   └── ui/                  # UI图片
├── icons/                   # 应用图标
└── qrc/                     # Qt资源文件
    ├── images.qrc           # 图片资源文件
    └── icons.qrc            # 图标资源文件
```

### 配置文件组织
```
config/
├── settings/                # 应用设置
│   ├── app_config.json      # 应用配置
│   ├── graphics_config.json # 图形设置
│   └── input_config.json    # 输入设置
└── scenarios/               # 仿真场景
    ├── scenario_01.json     # 场景1配置
    ├── scenario_02.json     # 场景2配置
    └── templates/           # 场景模板
```

### 数据文件组织
```
data/
├── maps/                    # 地图数据
│   ├── map_01.json         # 地图1数据
│   ├── map_02.json         # 地图2数据
│   └── templates/          # 地图模板
├── units/                  # 单位数据
│   ├── naval_units.json   # 海军单位数据
│   ├── air_units.json     # 空军单位数据
│   └── unit_templates.json # 单位模板
└── weapons/                # 武器数据
    ├── naval_weapons.json  # 海军武器数据
    ├── air_weapons.json    # 空军武器数据
    └── weapon_templates.json # 武器模板
```

## 开发流程

### 1. 开发阶段
1. **需求分析** - 明确功能需求和技术需求
2. **设计阶段** - 完成架构设计和详细设计
3. **编码实现** - 按模块进行开发
4. **单元测试** - 每个模块完成后进行测试
5. **集成测试** - 模块集成后进行测试
6. **系统测试** - 完整系统功能测试

### 2. 版本管理
- **主分支**: main - 稳定版本
- **开发分支**: develop - 开发版本
- **功能分支**: feature/* - 新功能开发
- **修复分支**: hotfix/* - 紧急修复

### 3. 构建流程
```bash
# 清理构建目录
./scripts/clean.sh

# 构建Debug版本
./scripts/build.sh debug

# 构建Release版本
./scripts/build.sh release

# 运行测试
./scripts/test.sh
```

## 技术选型说明

### 核心技术栈
- **Qt 5.14.2**: 跨平台GUI框架，提供丰富的界面组件
- **C++17**: 现代C++标准，提供更好的语言特性
- **qmake**: Qt官方构建系统，简单易用
- **OpenGL**: 3D图形渲染（可选）

### 第三方库
- **JSON**: 配置文件和数据文件格式
- **SQLite**: 轻量级数据库（可选）
- **Boost**: C++扩展库（按需使用）

## 性能要求

### 响应性能
- UI操作响应时间 < 100ms
- 地图渲染帧率 > 30fps
- 仿真计算实时性 < 50ms

### 资源使用
- 内存使用 < 1GB
- CPU使用率 < 80%
- 磁盘空间 < 500MB

## 扩展性设计

### 插件架构
预留插件接口，支持后续功能扩展：
- 新单位类型插件
- 新武器系统插件
- 新地图类型插件
- AI算法插件

### 国际化支持
- 使用Qt国际化框架
- 支持中文、英文界面
- 配置文件支持多语言

---

**文档版本**: 1.0  
**创建日期**: 2024年8月  
**维护者**: NAFSim开发团队  
**最后更新**: 2024年8月4日
