# NAFSim 用户手册

欢迎使用NAFSim（Naval Air Force Simulation）- 军事仿真类游戏！

## 目录

1. [快速入门](#快速入门)
2. [游戏界面](#游戏界面)
3. [基本操作](#基本操作)
4. [游戏模式](#游戏模式)
5. [单位系统](#单位系统)
6. [战斗系统](#战斗系统)
7. [设置选项](#设置选项)
8. [故障排除](#故障排除)

## 快速入门

### 系统要求

**最低配置**:
- 操作系统: macOS 10.14 或更高版本
- 处理器: Intel Core i5 或同等性能
- 内存: 4 GB RAM
- 显卡: 支持OpenGL 3.3的显卡
- 存储空间: 2 GB可用空间

**推荐配置**:
- 操作系统: macOS 11.0 或更高版本
- 处理器: Intel Core i7 或 Apple M1
- 内存: 8 GB RAM
- 显卡: 独立显卡或Apple M1集成显卡
- 存储空间: 4 GB可用空间

### 安装与启动

1. **下载游戏**: 从官方网站或GitHub下载最新版本
2. **解压文件**: 将下载的压缩包解压到任意目录
3. **启动游戏**: 双击 `NAFSim.app` 或运行可执行文件

### 首次运行

首次启动游戏时，系统会：
- 创建必要的配置文件
- 初始化游戏设置
- 显示欢迎界面

## 游戏界面

### 主界面布局

```
┌─────────────────────────────────────────────────────────┐
│  菜单栏                                                  │
├─────────────────────────────────────────────────────────┤
│  工具栏    [开始] [暂停] [停止] [设置]                    │
├──────────┬─────────────────────────────────┬────────────┤
│          │                                 │            │
│  单位列表 │         主地图视图               │  信息面板   │
│          │                                 │            │
│  - 蓝方   │                                 │  时间信息   │
│  - 红方   │                                 │  单位状态   │
│  - 中立   │                                 │  战斗日志   │
│          │                                 │            │
├──────────┴─────────────────────────────────┴────────────┤
│  状态栏   [仿真状态] [FPS] [时间比例]                     │
└─────────────────────────────────────────────────────────┘
```

### 界面组件说明

#### 1. 菜单栏
- **文件**: 新建、打开、保存场景
- **编辑**: 撤销、重做、复制、粘贴
- **视图**: 缩放、平移、显示选项
- **仿真**: 开始、暂停、停止、重置
- **工具**: 地图编辑器、单位编辑器
- **帮助**: 用户手册、关于

#### 2. 工具栏
- **开始按钮**: 启动仿真
- **暂停按钮**: 暂停/恢复仿真
- **停止按钮**: 停止仿真并重置
- **设置按钮**: 打开设置对话框

#### 3. 地图视图
- **主显示区域**: 显示地图、单位和战斗效果
- **缩放控制**: 鼠标滚轮缩放
- **平移控制**: 鼠标拖拽平移
- **单位选择**: 点击选择单位

#### 4. 信息面板
- **时间信息**: 当前仿真时间和时间比例
- **单位详情**: 选中单位的详细信息
- **战斗日志**: 实时战斗事件记录

## 基本操作

### 鼠标操作

| 操作 | 功能 |
|------|------|
| 左键点击 | 选择单位或目标点 |
| 左键拖拽 | 平移地图视图 |
| 右键点击 | 显示上下文菜单 |
| 滚轮滚动 | 缩放地图视图 |
| 双击 | 居中显示选中单位 |

### 键盘快捷键

| 快捷键 | 功能 |
|--------|------|
| Space | 开始/暂停仿真 |
| Esc | 停止仿真 |
| R | 重置仿真 |
| + / - | 调整时间比例 |
| F1 | 显示帮助 |
| Ctrl+N | 新建场景 |
| Ctrl+O | 打开场景 |
| Ctrl+S | 保存场景 |
| Ctrl+Q | 退出程序 |

### 视图控制

#### 缩放操作
- **放大**: 鼠标滚轮向上或按 `+` 键
- **缩小**: 鼠标滚轮向下或按 `-` 键
- **适合窗口**: 按 `F` 键
- **实际大小**: 按 `1` 键

#### 平移操作
- **鼠标拖拽**: 按住左键拖拽地图
- **键盘方向键**: 使用方向键平移
- **居中显示**: 双击单位或按 `C` 键

## 游戏模式

### 1. 场景模式
预设的仿真场景，包含特定的任务目标和胜利条件。

**特点**:
- 预定义的地图和单位配置
- 明确的任务目标
- 评分系统
- 难度等级

**操作流程**:
1. 选择场景文件
2. 查看场景简介和目标
3. 开始仿真
4. 完成任务目标

### 2. 自由模式
自定义仿真环境，可以自由配置地图、单位和规则。

**特点**:
- 完全自定义的仿真环境
- 无预设目标
- 实验性质的仿真
- 教学和训练用途

**操作流程**:
1. 创建新的空白场景
2. 添加地图和单位
3. 设置仿真参数
4. 开始自由仿真

### 3. 教学模式
专为学习和教学设计的模式，包含详细的说明和指导。

**特点**:
- 分步骤的教学内容
- 详细的操作指导
- 理论知识介绍
- 互动式学习

## 单位系统

### 海军单位

#### 驱逐舰
- **主要武器**: 127mm舰炮、导弹系统
- **探测能力**: 远程雷达、声纳系统
- **特点**: 多用途作战平台，攻防兼备

#### 护卫舰
- **主要武器**: 76mm舰炮、轻型导弹
- **探测能力**: 中程雷达、舰壳声纳
- **特点**: 护航和巡逻任务，机动性强

#### 潜艇
- **主要武器**: 鱼雷、巡航导弹
- **探测能力**: 被动声纳阵列
- **特点**: 隐蔽性强，水下作战

### 单位属性

每个单位都有以下基本属性：

- **生命值**: 单位的耐久度
- **装甲值**: 减少受到的伤害
- **速度**: 移动速度
- **燃料**: 行动能力限制
- **弹药**: 武器使用限制
- **探测范围**: 发现敌方单位的能力
- **隐蔽性**: 避免被发现的能力

## 战斗系统

### 战斗机制

#### 1. 探测阶段
- 单位使用雷达、声纳等传感器探测敌方
- 探测成功率受距离、天气、电子干扰影响
- 被探测单位会在地图上显示

#### 2. 瞄准阶段
- 选择武器类型和攻击目标
- 计算命中概率和预期伤害
- 考虑距离、相对速度、防御措施

#### 3. 攻击阶段
- 发射武器并计算弹道
- 目标可以使用防御措施
- 计算最终伤害和效果

#### 4. 结果阶段
- 应用伤害到目标单位
- 更新单位状态
- 记录战斗日志

### 武器类型

#### 舰炮
- **特点**: 射速快，弹药充足
- **适用**: 近距离作战，防空
- **限制**: 射程有限，精度一般

#### 导弹
- **特点**: 射程远，威力大
- **适用**: 远程打击，精确攻击
- **限制**: 弹药有限，成本高

#### 鱼雷
- **特点**: 水下攻击，威力巨大
- **适用**: 反舰、反潜作战
- **限制**: 速度慢，易被拦截

## 设置选项

### 图形设置
- **分辨率**: 调整游戏窗口大小
- **全屏模式**: 切换全屏/窗口模式
- **垂直同步**: 减少画面撕裂
- **抗锯齿**: 改善图像质量
- **纹理质量**: 调整纹理细节
- **阴影质量**: 调整阴影效果

### 音频设置
- **主音量**: 总体音量控制
- **音效音量**: 游戏音效音量
- **音乐音量**: 背景音乐音量
- **界面音效**: UI操作音效开关

### 游戏设置
- **时间比例**: 仿真速度控制
- **自动保存**: 定期保存游戏状态
- **显示FPS**: 显示帧率信息
- **战斗日志**: 详细程度设置
- **单位标签**: 显示单位信息

### 控制设置
- **鼠标灵敏度**: 调整鼠标响应速度
- **键盘重复**: 按键重复设置
- **快捷键**: 自定义快捷键绑定

## 故障排除

### 常见问题

#### 1. 游戏无法启动
**可能原因**:
- Qt运行库缺失
- 系统版本过低
- 权限不足

**解决方案**:
- 安装Qt 5.14.2运行库
- 升级到支持的系统版本
- 以管理员权限运行

#### 2. 性能问题
**症状**:
- 帧率过低
- 响应延迟
- 卡顿现象

**解决方案**:
- 降低图形设置
- 关闭不必要的后台程序
- 检查硬件配置

#### 3. 显示问题
**症状**:
- 界面显示异常
- 文字模糊
- 颜色错误

**解决方案**:
- 更新显卡驱动
- 调整显示缩放设置
- 检查显示器设置

### 日志文件

游戏运行时会生成日志文件，位于：
- macOS: `~/Library/Application Support/NAFSim/logs/`
- 项目目录: `logs/`

日志文件包含：
- 错误信息
- 警告信息
- 调试信息
- 性能数据

### 联系支持

如果遇到无法解决的问题，请：
1. 查看日志文件
2. 记录问题重现步骤
3. 通过以下方式联系支持：
   - GitHub Issues: https://github.com/xixilidao/NAFSim/issues
   - 邮箱: <EMAIL>

---

**版本**: 1.0  
**最后更新**: 2024年8月4日  
**维护者**: NAFSim开发团队
