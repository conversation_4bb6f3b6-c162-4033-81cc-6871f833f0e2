/**
 * @file basic_usage.cpp
 * @brief NAFSim 基础使用示例
 * 
 * 演示如何使用NAFSim的基本功能，包括创建单位、设置场景等
 * 
 * <AUTHOR>
 * @date 2024-08-04
 * @version 1.0
 */

#include <QApplication>
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QFile>

/**
 * @brief 示例：加载配置文件
 * 
 * 演示如何加载和解析JSON配置文件
 */
void loadConfigExample()
{
    qDebug() << "=== 配置文件加载示例 ===";
    
    // 加载应用配置文件
    QFile configFile("config/settings/app_config.json");
    if (configFile.open(QIODevice::ReadOnly)) {
        QByteArray data = configFile.readAll();
        QJsonDocument doc = QJsonDocument::fromJson(data);
        QJsonObject config = doc.object();
        
        // 读取应用信息
        QJsonObject app = config["application"].toObject();
        qDebug() << "应用名称:" << app["name"].toString();
        qDebug() << "应用版本:" << app["version"].toString();
        qDebug() << "语言设置:" << app["language"].toString();
        
        // 读取窗口设置
        QJsonObject window = config["window"].toObject();
        qDebug() << "窗口大小:" << window["width"].toInt() << "x" << window["height"].toInt();
        qDebug() << "全屏模式:" << (window["fullscreen"].toBool() ? "是" : "否");
        
        configFile.close();
    } else {
        qDebug() << "无法打开配置文件";
    }
}

/**
 * @brief 示例：加载场景数据
 * 
 * 演示如何加载和解析场景配置文件
 */
void loadScenarioExample()
{
    qDebug() << "\n=== 场景数据加载示例 ===";
    
    // 加载场景配置文件
    QFile scenarioFile("config/scenarios/scenario_01.json");
    if (scenarioFile.open(QIODevice::ReadOnly)) {
        QByteArray data = scenarioFile.readAll();
        QJsonDocument doc = QJsonDocument::fromJson(data);
        QJsonObject scenario = doc.object();
        
        // 读取场景信息
        QJsonObject info = scenario["scenario"].toObject();
        qDebug() << "场景名称:" << info["name"].toString();
        qDebug() << "场景描述:" << info["description"].toString();
        qDebug() << "难度等级:" << info["difficulty"].toString();
        
        // 读取地图信息
        QJsonObject map = scenario["map"].toObject();
        qDebug() << "地图名称:" << map["name"].toString();
        qDebug() << "地图大小:" << map["width"].toInt() << "x" << map["height"].toInt();
        
        // 读取部队信息
        QJsonObject forces = scenario["forces"].toObject();
        QJsonObject blueForce = forces["blue_force"].toObject();
        QJsonObject redForce = forces["red_force"].toObject();
        
        qDebug() << "蓝方:" << blueForce["name"].toString();
        qDebug() << "红方:" << redForce["name"].toString();
        qDebug() << "红方AI控制:" << (redForce["ai_controlled"].toBool() ? "是" : "否");
        
        scenarioFile.close();
    } else {
        qDebug() << "无法打开场景文件";
    }
}

/**
 * @brief 示例：加载单位数据
 * 
 * 演示如何加载和解析单位数据文件
 */
void loadUnitsExample()
{
    qDebug() << "\n=== 单位数据加载示例 ===";
    
    // 加载海军单位数据文件
    QFile unitsFile("data/units/naval_units.json");
    if (unitsFile.open(QIODevice::ReadOnly)) {
        QByteArray data = unitsFile.readAll();
        QJsonDocument doc = QJsonDocument::fromJson(data);
        QJsonObject units = doc.object();
        
        // 读取海军单位数据
        QJsonObject navalUnits = units["naval_units"].toObject();
        
        // 遍历所有单位类型
        for (auto it = navalUnits.begin(); it != navalUnits.end(); ++it) {
            QString unitType = it.key();
            QJsonObject unitData = it.value().toObject();
            
            qDebug() << "单位类型:" << unitType;
            qDebug() << "单位名称:" << unitData["name"].toString();
            qDebug() << "单位类别:" << unitData["category"].toString();
            
            // 读取规格参数
            QJsonObject specs = unitData["specifications"].toObject();
            qDebug() << "  长度:" << specs["length"].toDouble() << "米";
            qDebug() << "  最大速度:" << specs["max_speed"].toInt() << "节";
            qDebug() << "  船员:" << specs["crew"].toInt() << "人";
            
            // 读取战斗参数
            QJsonObject combat = unitData["combat_stats"].toObject();
            qDebug() << "  生命值:" << combat["max_health"].toInt();
            qDebug() << "  装甲:" << combat["armor"].toInt();
            qDebug() << "  雷达范围:" << combat["radar_range"].toInt() << "公里";
            
            qDebug() << ""; // 空行分隔
        }
        
        unitsFile.close();
    } else {
        qDebug() << "无法打开单位数据文件";
    }
}

/**
 * @brief 示例：目录结构演示
 * 
 * 演示项目目录结构的使用方法
 */
void directoryStructureExample()
{
    qDebug() << "\n=== 目录结构演示 ===";
    
    qDebug() << "项目目录结构说明:";
    qDebug() << "├── bin/           - 可执行文件输出目录";
    qDebug() << "├── src/           - 源代码目录";
    qDebug() << "│   ├── core/      - 核心逻辑模块";
    qDebug() << "│   ├── ui/        - 用户界面模块";
    qDebug() << "│   ├── simulation/- 仿真逻辑模块";
    qDebug() << "│   └── utils/     - 工具类模块";
    qDebug() << "├── resources/     - Qt资源文件";
    qDebug() << "├── config/        - 配置文件";
    qDebug() << "├── data/          - 游戏数据文件";
    qDebug() << "├── docs/          - 项目文档";
    qDebug() << "├── tests/         - 测试代码";
    qDebug() << "├── example/       - 示例代码(当前文件所在目录)";
    qDebug() << "├── scripts/       - 构建脚本";
    qDebug() << "└── tools/         - 开发工具";
    
    qDebug() << "\n使用建议:";
    qDebug() << "1. 源代码按功能模块组织在src/目录下";
    qDebug() << "2. 配置文件统一放在config/目录下";
    qDebug() << "3. 游戏数据文件放在data/目录下";
    qDebug() << "4. 使用scripts/目录下的脚本进行构建和测试";
    qDebug() << "5. 文档和示例代码帮助理解项目结构";
}

/**
 * @brief 主函数
 * 
 * 运行所有示例代码
 */
int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    qDebug() << "NAFSim 基础使用示例";
    qDebug() << "====================";
    
    // 运行各种示例
    loadConfigExample();
    loadScenarioExample();
    loadUnitsExample();
    directoryStructureExample();
    
    qDebug() << "\n示例运行完成！";
    qDebug() << "更多信息请参考:";
    qDebug() << "- README.md - 项目说明";
    qDebug() << "- CODING_RULES.md - 编码规范";
    qDebug() << "- docs/design/PROJECT_DESIGN.md - 设计文档";
    
    return 0;
}
