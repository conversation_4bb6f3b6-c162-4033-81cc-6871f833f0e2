#!/bin/bash

# NAFSim 构建脚本
# 用法: ./build.sh [debug|release]

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BUILD_TYPE=${1:-debug}  # 默认为debug模式

# Qt路径配置
QT_PATH="/Users/<USER>/Qt5.14.2/5.14.2/clang_64/bin"
QMAKE="$QT_PATH/qmake"

echo -e "${BLUE}NAFSim 构建脚本${NC}"
echo -e "${BLUE}==================${NC}"
echo "项目根目录: $PROJECT_ROOT"
echo "构建类型: $BUILD_TYPE"
echo ""

# 检查Qt安装
if [ ! -f "$QMAKE" ]; then
    echo -e "${RED}错误: 找不到qmake，请检查Qt安装路径${NC}"
    echo "当前路径: $QMAKE"
    exit 1
fi

# 检查项目文件
PRO_FILE="$PROJECT_ROOT/NAFSim.pro"
if [ ! -f "$PRO_FILE" ]; then
    echo -e "${RED}错误: 找不到项目文件 NAFSim.pro${NC}"
    exit 1
fi

# 创建构建目录
BUILD_DIR="$PROJECT_ROOT/build/$BUILD_TYPE"
mkdir -p "$BUILD_DIR"

# 进入构建目录
cd "$BUILD_DIR"

echo -e "${YELLOW}正在配置项目...${NC}"

# 配置项目
if [ "$BUILD_TYPE" = "release" ]; then
    "$QMAKE" "$PRO_FILE" -spec macx-clang CONFIG+=release CONFIG-=debug
else
    "$QMAKE" "$PRO_FILE" -spec macx-clang CONFIG+=debug CONFIG-=release
fi

if [ $? -ne 0 ]; then
    echo -e "${RED}qmake 配置失败${NC}"
    exit 1
fi

echo -e "${YELLOW}正在编译项目...${NC}"

# 编译项目
make -j$(sysctl -n hw.ncpu)

if [ $? -ne 0 ]; then
    echo -e "${RED}编译失败${NC}"
    exit 1
fi

# 复制可执行文件到bin目录
BIN_DIR="$PROJECT_ROOT/bin/$BUILD_TYPE"
mkdir -p "$BIN_DIR"

# 查找生成的可执行文件
if [ -f "NAFSim.app/Contents/MacOS/NAFSim" ]; then
    cp -r NAFSim.app "$BIN_DIR/"
    echo -e "${GREEN}可执行文件已复制到: $BIN_DIR/NAFSim.app${NC}"
elif [ -f "NAFSim" ]; then
    cp NAFSim "$BIN_DIR/"
    echo -e "${GREEN}可执行文件已复制到: $BIN_DIR/NAFSim${NC}"
else
    echo -e "${YELLOW}警告: 未找到可执行文件${NC}"
fi

echo ""
echo -e "${GREEN}构建完成！${NC}"
echo -e "${GREEN}构建类型: $BUILD_TYPE${NC}"
echo -e "${GREEN}输出目录: $BIN_DIR${NC}"
echo ""

# 显示构建信息
echo -e "${BLUE}构建信息:${NC}"
echo "- 构建目录: $BUILD_DIR"
echo "- Qt版本: $(\"$QMAKE\" -query QT_VERSION)"
echo "- 编译器: clang"
echo "- 平台: macOS"
