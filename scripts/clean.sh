#!/bin/bash

# NAFSim 清理脚本
# 清理构建文件和临时文件

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo -e "${BLUE}NAFSim 清理脚本${NC}"
echo -e "${BLUE}==================${NC}"
echo "项目根目录: $PROJECT_ROOT"
echo ""

cd "$PROJECT_ROOT"

echo -e "${YELLOW}正在清理构建文件...${NC}"

# 清理构建目录
if [ -d "build" ]; then
    rm -rf build/*
    echo "- 已清理 build/ 目录"
fi

# 清理bin目录中的可执行文件
if [ -d "bin" ]; then
    find bin -name "*.app" -type d -exec rm -rf {} + 2>/dev/null || true
    find bin -name "NAFSim" -type f -exec rm -f {} + 2>/dev/null || true
    find bin -name "*.exe" -type f -exec rm -f {} + 2>/dev/null || true
    echo "- 已清理 bin/ 目录中的可执行文件"
fi

# 清理临时文件
if [ -d "temp" ]; then
    rm -rf temp/*
    echo "- 已清理 temp/ 目录"
fi

# 清理日志文件
if [ -d "logs" ]; then
    rm -rf logs/*
    echo "- 已清理 logs/ 目录"
fi

# 清理Qt生成的文件
find . -name "Makefile*" -type f -delete 2>/dev/null || true
find . -name "*.o" -type f -delete 2>/dev/null || true
find . -name "moc_*.cpp" -type f -delete 2>/dev/null || true
find . -name "moc_*.h" -type f -delete 2>/dev/null || true
find . -name "ui_*.h" -type f -delete 2>/dev/null || true
find . -name "qrc_*.cpp" -type f -delete 2>/dev/null || true
find . -name "*.pro.user*" -type f -delete 2>/dev/null || true
find . -name ".qmake.stash" -type f -delete 2>/dev/null || true
find . -name ".qmake.cache" -type f -delete 2>/dev/null || true

echo "- 已清理Qt生成的临时文件"

# 清理系统临时文件
find . -name ".DS_Store" -type f -delete 2>/dev/null || true
find . -name "Thumbs.db" -type f -delete 2>/dev/null || true
find . -name "*.tmp" -type f -delete 2>/dev/null || true

echo "- 已清理系统临时文件"

echo ""
echo -e "${GREEN}清理完成！${NC}"
