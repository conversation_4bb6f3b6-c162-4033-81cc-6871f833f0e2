/**
 * @file main.cpp
 * @brief NAFSim 主程序入口
 * 
 * 军事仿真类游戏的主程序入口文件，负责初始化Qt应用程序
 * 和启动主窗口。
 * 
 * <AUTHOR>
 * @date 2024-08-04
 * @version 1.0
 */

#include <QApplication>
#include <QDir>
#include <QStandardPaths>
#include <QDebug>
#include <QStyleFactory>
#include <QFont>

/**
 * @brief 初始化应用程序目录
 * 
 * 创建必要的应用程序目录，如日志目录、配置目录等
 */
void initializeAppDirectories()
{
    // 获取应用程序数据目录
    QString appDataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    
    // 创建必要的目录
    QStringList dirs = {
        appDataPath + "/logs",
        appDataPath + "/config",
        appDataPath + "/temp",
        appDataPath + "/saves"
    };
    
    for (const QString& dir : dirs) {
        QDir().mkpath(dir);
    }
    
    qDebug() << "应用程序目录已初始化:" << appDataPath;
}

/**
 * @brief 设置应用程序样式
 * 
 * 配置应用程序的外观和字体
 */
void setupApplicationStyle(QApplication& app)
{
    // 设置应用程序样式
    app.setStyle(QStyleFactory::create("Fusion"));
    
    // 设置应用程序字体
    QFont font("Arial", 10);
    app.setFont(font);
    
    // 设置应用程序调色板（深色主题）
    QPalette darkPalette;
    darkPalette.setColor(QPalette::Window, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::WindowText, Qt::white);
    darkPalette.setColor(QPalette::Base, QColor(25, 25, 25));
    darkPalette.setColor(QPalette::AlternateBase, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ToolTipBase, Qt::white);
    darkPalette.setColor(QPalette::ToolTipText, Qt::white);
    darkPalette.setColor(QPalette::Text, Qt::white);
    darkPalette.setColor(QPalette::Button, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ButtonText, Qt::white);
    darkPalette.setColor(QPalette::BrightText, Qt::red);
    darkPalette.setColor(QPalette::Link, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::Highlight, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::HighlightedText, Qt::black);
    
    app.setPalette(darkPalette);
    
    qDebug() << "应用程序样式已设置";
}

/**
 * @brief 主函数
 * 
 * 程序的入口点，初始化Qt应用程序并启动主窗口
 * 
 * @param argc 命令行参数个数
 * @param argv 命令行参数数组
 * @return int 程序退出代码
 */
int main(int argc, char *argv[])
{
    // 创建Qt应用程序实例
    QApplication app(argc, argv);
    
    // 设置应用程序信息
    app.setApplicationName("NAFSim");
    app.setApplicationDisplayName("Naval Air Force Simulation");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("NAFSim Team");
    app.setOrganizationDomain("nafsim.org");
    
    qDebug() << "NAFSim 启动中...";
    qDebug() << "版本:" << app.applicationVersion();
    qDebug() << "Qt版本:" << qVersion();
    
    // 初始化应用程序目录
    initializeAppDirectories();
    
    // 设置应用程序样式
    setupApplicationStyle(app);
    
    // TODO: 创建并显示主窗口
    // MainWindow window;
    // window.show();
    
    // 临时显示消息
    qDebug() << "NAFSim 已启动，但主窗口尚未实现";
    qDebug() << "请实现 MainWindow 类后重新编译";
    
    // 暂时直接退出，等待主窗口实现
    qDebug() << "程序将在3秒后退出...";
    
    // 简单的消息循环，3秒后退出
    QTimer::singleShot(3000, &app, &QApplication::quit);
    
    // 进入事件循环
    int result = app.exec();
    
    qDebug() << "NAFSim 已退出，退出代码:" << result;
    
    return result;
}

// 包含必要的Qt头文件
#include <QTimer>
