# NAFSim 测试框架

本目录包含NAFSim项目的所有测试代码，包括单元测试和集成测试。

## 测试结构

```
tests/
├── unit_tests/           # 单元测试
│   ├── core/            # 核心模块测试
│   ├── simulation/      # 仿真模块测试
│   ├── ui/              # UI模块测试
│   └── utils/           # 工具类测试
├── integration_tests/   # 集成测试
│   ├── scenarios/       # 场景测试
│   ├── performance/     # 性能测试
│   └── compatibility/   # 兼容性测试
└── test_data/          # 测试数据文件
```

## 测试框架

### 使用的测试框架
- **Qt Test Framework**: Qt官方测试框架
- **Google Test**: C++单元测试框架（可选）
- **Qt Quick Test**: QML测试框架（如果使用QML）

### 测试类型

#### 1. 单元测试 (Unit Tests)
- **目的**: 测试单个类或函数的功能
- **范围**: 每个模块的核心功能
- **运行频率**: 每次代码提交

#### 2. 集成测试 (Integration Tests)
- **目的**: 测试模块间的交互
- **范围**: 完整的功能流程
- **运行频率**: 每日构建

#### 3. 性能测试 (Performance Tests)
- **目的**: 验证性能指标
- **范围**: 关键算法和渲染性能
- **运行频率**: 版本发布前

## 测试规范

### 命名规范
```cpp
// 测试类命名: Test + 被测试类名
class TestSimulationEngine : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();        // 测试开始前执行
    void cleanupTestCase();     // 测试结束后执行
    void init();                // 每个测试前执行
    void cleanup();             // 每个测试后执行
    
    // 测试函数命名: test + 功能描述
    void testInitialization();
    void testUnitCreation();
    void testBattleCalculation();
};
```

### 测试文件组织
```cpp
// 文件命名: test_[模块名].cpp
// 例如: test_simulation_engine.cpp

#include <QtTest/QtTest>
#include "simulation_engine.h"

class TestSimulationEngine : public QObject
{
    Q_OBJECT

private:
    SimulationEngine* m_engine;

private slots:
    void initTestCase()
    {
        // 测试套件初始化
        m_engine = new SimulationEngine();
    }
    
    void cleanupTestCase()
    {
        // 测试套件清理
        delete m_engine;
    }
    
    void testInitialization()
    {
        QVERIFY(m_engine != nullptr);
        QCOMPARE(m_engine->getState(), SimulationEngine::Stopped);
    }
};

QTEST_MAIN(TestSimulationEngine)
#include "test_simulation_engine.moc"
```

## 运行测试

### 使用脚本运行
```bash
# 运行所有测试
./scripts/test.sh

# 运行单元测试
./scripts/test.sh unit

# 运行集成测试
./scripts/test.sh integration
```

### 手动运行
```bash
# 构建测试
cd tests/unit_tests/core
qmake test_simulation_engine.pro
make

# 运行测试
./test_simulation_engine
```

### 测试报告
测试结果会保存在 `logs/tests/` 目录下：
- `test_report_YYYYMMDD_HHMMSS.txt` - 详细测试报告
- `test_summary.xml` - JUnit格式的测试结果
- `coverage_report.html` - 代码覆盖率报告（如果启用）

## 测试数据

### 测试数据管理
```cpp
// 使用Qt资源系统管理测试数据
class TestDataManager
{
public:
    static QString getTestDataPath(const QString& filename)
    {
        return QString(":/test_data/%1").arg(filename);
    }
    
    static QJsonObject loadTestConfig(const QString& configName)
    {
        QString path = getTestDataPath(configName + ".json");
        QFile file(path);
        if (file.open(QIODevice::ReadOnly)) {
            return QJsonDocument::fromJson(file.readAll()).object();
        }
        return QJsonObject();
    }
};
```

### 模拟数据
```cpp
// 创建模拟对象用于测试
class MockUnit : public Unit
{
public:
    MockUnit() : Unit("test_unit", UnitType::Naval) {}
    
    // 重写虚函数用于测试
    void update(double deltaTime) override
    {
        m_updateCalled = true;
        m_lastDeltaTime = deltaTime;
    }
    
    bool wasUpdateCalled() const { return m_updateCalled; }
    double getLastDeltaTime() const { return m_lastDeltaTime; }

private:
    bool m_updateCalled = false;
    double m_lastDeltaTime = 0.0;
};
```

## 测试最佳实践

### 1. 测试原则
- **独立性**: 每个测试应该独立运行
- **可重复性**: 测试结果应该一致
- **快速性**: 单元测试应该快速执行
- **清晰性**: 测试代码应该易于理解

### 2. 断言使用
```cpp
// 基本断言
QVERIFY(condition);                    // 验证条件为真
QCOMPARE(actual, expected);            // 比较两个值
QVERIFY2(condition, "error message");  // 带错误信息的验证

// 浮点数比较
QCOMPARE(actual, expected);            // 精确比较
qFuzzyCompare(actual, expected);       // 模糊比较

// 异常测试
QVERIFY_EXCEPTION_THROWN(code, ExceptionType);
```

### 3. 测试数据准备
```cpp
void TestClass::init()
{
    // 每个测试前的准备工作
    m_testObject = new TestObject();
    m_testObject->initialize();
}

void TestClass::cleanup()
{
    // 每个测试后的清理工作
    delete m_testObject;
    m_testObject = nullptr;
}
```

## 持续集成

### GitHub Actions配置
```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: macos-latest
    steps:
    - uses: actions/checkout@v2
    - name: Install Qt
      uses: jurplel/install-qt-action@v2
    - name: Build and Test
      run: |
        ./scripts/build.sh debug
        ./scripts/test.sh
```

### 代码覆盖率
```bash
# 启用代码覆盖率
qmake CONFIG+=coverage
make
./test_executable
gcov *.gcno
lcov --capture --directory . --output-file coverage.info
genhtml coverage.info --output-directory coverage_html
```

## 性能基准测试

### 基准测试示例
```cpp
void TestPerformance::benchmarkBattleCalculation()
{
    SimulationEngine engine;
    // 准备测试数据...
    
    QBENCHMARK {
        engine.calculateBattle();
    }
}
```

### 性能指标
- **帧率**: 目标 > 30 FPS
- **内存使用**: < 1GB
- **启动时间**: < 5秒
- **响应时间**: UI操作 < 100ms

## 贡献指南

### 添加新测试
1. 确定测试类型（单元测试/集成测试）
2. 在相应目录创建测试文件
3. 遵循命名规范和代码风格
4. 包含足够的测试用例
5. 更新测试文档

### 测试审查清单
- [ ] 测试覆盖所有公共接口
- [ ] 包含边界条件测试
- [ ] 错误处理测试完整
- [ ] 性能测试合理
- [ ] 测试代码清晰易懂
- [ ] 无内存泄漏

---

**注意**: 当前测试框架正在建设中，欢迎贡献测试用例！
