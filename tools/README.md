# NAFSim 开发工具

本目录包含NAFSim项目的各种开发工具和实用程序。

## 工具列表

### 规划中的工具

#### 1. 地图编辑器 (Map Editor)
- **目录**: `map_editor/`
- **功能**: 可视化地图编辑工具
- **特性**:
  - 拖拽式地形编辑
  - 单位放置和配置
  - 地图预览和测试
  - 导出为游戏可用格式

#### 2. 场景编辑器 (Scenario Editor)
- **目录**: `scenario_editor/`
- **功能**: 仿真场景配置工具
- **特性**:
  - 图形化场景配置
  - 单位部署设置
  - 任务目标定义
  - 胜利条件配置

#### 3. 单位编辑器 (Unit Editor)
- **目录**: `unit_editor/`
- **功能**: 单位数据编辑工具
- **特性**:
  - 单位属性编辑
  - 武器系统配置
  - 传感器参数设置
  - 3D模型预览

#### 4. 数据转换器 (Data Converter)
- **目录**: `data_converter/`
- **功能**: 数据格式转换工具
- **特性**:
  - JSON与其他格式互转
  - 批量数据处理
  - 数据验证和修复
  - 版本兼容性处理

#### 5. 性能分析器 (Performance Profiler)
- **目录**: `profiler/`
- **功能**: 性能分析和优化工具
- **特性**:
  - 实时性能监控
  - 内存使用分析
  - 帧率统计
  - 瓶颈识别

#### 6. 资源管理器 (Resource Manager)
- **目录**: `resource_manager/`
- **功能**: 游戏资源管理工具
- **特性**:
  - 资源文件组织
  - 纹理压缩优化
  - 音频格式转换
  - 资源打包

## 使用说明

### 开发环境要求
- Qt 5.14.2 或更高版本
- C++17 兼容编译器
- 足够的磁盘空间用于工具和资源文件

### 构建工具
```bash
# 进入工具目录
cd tools/

# 构建特定工具（以地图编辑器为例）
cd map_editor/
qmake map_editor.pro
make

# 运行工具
./map_editor
```

### 工具集成
所有工具都设计为可以与主项目集成：
- 共享相同的数据格式
- 使用统一的配置系统
- 支持热重载和实时预览
- 提供命令行接口用于自动化

## 开发计划

### 第一阶段 (v1.0)
- [ ] 基础地图编辑器
- [ ] 简单场景编辑器
- [ ] 数据验证工具

### 第二阶段 (v1.1)
- [ ] 高级地图编辑功能
- [ ] 单位编辑器
- [ ] 性能分析工具

### 第三阶段 (v1.2)
- [ ] 资源管理器
- [ ] 批量处理工具
- [ ] 插件系统

## 贡献指南

### 添加新工具
1. 在`tools/`目录下创建新的子目录
2. 遵循项目的编码规范
3. 提供详细的README文档
4. 包含使用示例和测试用例
5. 确保与主项目的兼容性

### 工具开发规范
- 使用Qt框架保持一致性
- 遵循主项目的设计模式
- 提供图形界面和命令行接口
- 支持配置文件和参数设置
- 包含错误处理和用户反馈

## 技术架构

### 共享组件
```cpp
// 工具基类
class ToolBase {
public:
    virtual bool initialize() = 0;
    virtual void run() = 0;
    virtual void cleanup() = 0;
};

// 配置管理
class ToolConfig {
public:
    bool loadConfig(const QString& configFile);
    void saveConfig(const QString& configFile);
    QVariant getValue(const QString& key);
    void setValue(const QString& key, const QVariant& value);
};

// 数据接口
class DataInterface {
public:
    virtual bool loadData(const QString& dataFile) = 0;
    virtual bool saveData(const QString& dataFile) = 0;
    virtual bool validateData() = 0;
};
```

### 插件接口
```cpp
// 插件接口
class ToolPlugin {
public:
    virtual QString getName() const = 0;
    virtual QString getVersion() const = 0;
    virtual bool initialize(ToolBase* tool) = 0;
    virtual void execute() = 0;
};
```

## 许可证

所有工具遵循与主项目相同的MIT许可证。

## 联系方式

如有工具相关的问题或建议，请通过以下方式联系：
- 项目Issues: https://github.com/xixilidao/NAFSim/issues
- 邮箱: <EMAIL>

---

**注意**: 当前大部分工具仍在规划阶段，欢迎贡献代码和想法！
